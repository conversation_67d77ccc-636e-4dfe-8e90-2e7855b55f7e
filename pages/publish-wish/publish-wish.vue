<template>
  <YjNavBar title="发布旅行心愿"
            @load="({ height, paddingBottom }) => contentStyle.paddingTop = `${height + paddingBottom}px`"
            @not-found="navTo('pages/index/index')"></YjNavBar>
  <view :style="contentStyle" class="publish-wish">
    <!-- 出发地和目的地 -->
    <view class="section dest-budget-day">
      <view class="section-body">
        <view class="from-to">
          <view class="left">
            <text></text>
            <view></view>
            <text class="iconfont icon-daohangdizhiweizhi"/>
          </view>
          <view class="dest">
            <MyInput2 v-model="formData.from" placeholder="请输入出发地"/>
            <view class="to" @tap="navTo('pages/search/poi')">
              {{ formData.to ? formData.to : '请选择目的地' }}
            </view>
          </view>
        </view>
        <view class="budget">
          <view class="switch">
            <view :class="{
              active: formData.budget_type === BudgetType.Single,
            }" @tap="formData.budget_type = BudgetType.Single">
              <text class="iconfont icon-user"/>
              单人预算
            </view>
            <view :class="{
              active: formData.budget_type === BudgetType.Team,
            }" @tap="formData.budget_type = BudgetType.Team">
              <text class="iconfont icon-ren"/>
              整体预算
            </view>
          </view>
          <view class="tips">单人最高可80%免单
            <text class="iconfont icon-wenhao"/>
          </view>
          <view class="budget-options">
            <view v-for="(item, index) in budgetOptions" :key="index" :class="{
              active: item === formData.budget,
            }" class="budget-item" @tap="() => handleBudgetChange(item)">
              {{ item }}
            </view>
            <view v-if="!budgetCustom" class="budget-item" @tap="budgetCustom = true">
              自定义
            </view>
            <MyInput2 v-else placeholder="自定义" size="small" @change="e => formData.budget = e"/>
          </view>
        </view>
        <!-- 出发日期 -->
        <view class="date">
          <view class="switch">
            <view :class="{
              active: formDateDateType === DateType.Date
            }" @tap="openCalendar(DateType.Date)">
              <text class="iconfont icon-rili"/>
              具体日期
            </view>
            <view :class="{
              active: formDateDateType === DateType.Month
            }" @tap="openCalendar(DateType.Month)">
              <text class="iconfont icon-yuefen"/>
              月份选择
            </view>
            <view :class="{
              active: formDateDateType === DateType.Unknown
            }" @tap="openCalendar(DateType.Unknown)">
              <text class="iconfont icon-24gf-hourglass"/>
              待定
            </view>
          </view>
          <view v-if="formDateDateType !== DateType.Unknown && dateText.length > 0" class="date-text">{{
              dateText
            }}
          </view>
        </view>
      </view>

    </view>

    <!-- 必做实现 -->
    <view class="section todos">
      <view class="section-title">
        想做的事
        <view @tap="onAi('todos')">
          <image src="/static/wish/star.png" mode="widthFix"/>
          AI帮你想一下
        </view>
      </view>
      <view class="section-body">
        <view class="list">
          <view v-for="(item, index) in formData.todos" :key="index" class="todo-item">
            <view>
              {{ item.todo }}
            </view>
            <view class="right">
              <MyCheckBox label="必做" @change="e => item.is_must = e"/>
              <text class="iconfont icon-guanbi" @tap="removeTodo(index)"></text>
            </view>
          </view>
        </view>
        <MyInput2 v-if="showAddTodo" v-model="newTodo.todo" class="todo-input" placeholder="请输入想做的事"
                  @confirm="onConfirmAddTodo"/>

        <view class="btn-add" @tap="addTodo">
          <text class="iconfont icon-jia2"/>
          添加想做的事
        </view>
      </view>

    </view>

    <!-- 同行人设置 -->
    <view class="section with-peoples">
      <view class="section-title">同行人设置</view>
      <view class="section-body">
        <view class="peoples">
          <view class="left">
            期望同行人数
            <text class="tips">（包含自己）</text>
          </view>

          <view class="right">
            <MyInputNumber v-model="formData.total_people" max="30" min="2"></MyInputNumber>
          </view>
        </view>
        <textarea style="max-height: 200rpx;" v-model="formData.member_desc" placeholder="补充说明（选填）"/>
      </view>
    </view>

    <!-- 心愿标题和描述 -->
    <view class="section wish-subject">
      <view class="section-title">心愿主题
        <view @tap="onAi('subject')">
          <image src="/static/wish/star.png" mode="widthFix"/>
          AI帮你想一下
        </view>
      </view>
      <view class="section-body">
        <MyInput2 :border="false" v-model="formData.title" maxlength="200" placeholder="填写你的心愿主题…"/>
      </view>
    </view>
    <view class="section wish-content">
      <view class="section-title">
        心愿描述
        <view @tap="onAi('desc')">
          <image src="/static/wish/star.png" mode="widthFix"/>
          AI帮你想一下
        </view>
      </view>
      <view class="section-body">
        <textarea style="max-height: 200rpx;" v-model="formData.wish_desc" maxlength="200"
                  placeholder="描述一下你的旅行心愿…"/>
      </view>
    </view>

    <!-- 标签 -->
    <view class="section tags">
      <text class="section-title">标签</text>
      <view class="section-body">
        <view class="list">
          <view v-for="(tag, index) in formData.tags" :key="index">
            <YjCloseableTag @close="removeTag(index)">
              <view class="list-item">
                {{ `#${tag}` }}
              </view>
            </YjCloseableTag>
          </view>

          <view class="btn-add-tag" @tap="addTag">
            <view>
              <text class="iconfont icon-jia2"/>
              添加标签
            </view>
          </view>
        </view>
      </view>

    </view>

    <!-- 图片和视频 -->
    <view class="section video">
      <text class="section-title">
        图片和视频
        <text class="tips">（最多9张，单个文件最大不可超过20M）</text>
      </text>
      <view class="section-body nobody">
        <view class="media-list">
          <view class="btn-upload" @tap="uploadMedia">
            <text class="iconfont icon-jia2"/>
            上传
          </view>
          <view v-for="(medias, index) in formData.medias" :key="index" class="media-item">
            <YjCloseableTag
                @close="removemedias(index)">
              <image v-if="medias.type === MediaTypeImage.Image" :src="medias.url"/>
              <video v-else :show-center-play-btn="false" :src="medias.url"/>
            </YjCloseableTag>
          </view>
        </view>
      </view>

    </view>

    <!-- 联系方式 -->
    <view class="section tel">
      <text class="section-title">联系方式</text>
      <view class="section-body">
        <text class="iconfont icon-dianhua1"/>
        <MyInput2 placeholder="请输入手机号" :border="false" v-model="formData.phone"/>
      </view>
      <view class="note">
        <text class="iconfont icon-guanyuwomen"/>
        联系方式仅后台可见，为确保行程顺利，请务必填写真实有效的联系方式。
      </view>
    </view>

    <!-- 公开范围 -->
    <view class="section open_scope">
      <text class="section-title">公开范围</text>
      <view class="section-body nobody">
        <radio-group @change="handleOpenScopeChange">
          <label>
            <radio :value="WishOpenScope.Public"/>
            公开
            <text>（所有人可见，会进入心愿广场）</text>
          </label>
          <label>
            <radio :value="WishOpenScope.Private"/>
            私密
            <text>（仅自己可见）</text>
          </label>
        </radio-group>
      </view>

    </view>

    <MyButton type="primary" @tap="onSubmit">发布心愿</MyButton>
    <!-- 日历组件（弹出层） -->
    <YjCalendar v-if="showCalendar" :holiday-callback="holiday4Calendar" :max-range="[new Date()]" :mode="calendarMode"
                popup @close="showCalendar = false" @confirm="handleCalendarConfirm"/>
  </view>

</template>

<script setup>
import {computed, ref, watch} from 'vue';
import YjCalendar from '@/components/YjCalendar/YjCalendar.vue';
import {deepToRaw, holiday4Calendar, navTo, parseFileSize, showPrompt, showToast} from "@/utils";
import YjNavBar from "@/components/YjNavBar/YjNavBar.vue";
import MyInput2 from "@/components/MyInput2/MyInput2.vue";
import {BudgetType, Disable, Enable, WishMediaType, WishOpenScope} from "@/utils/constmap";
import dayjs from "dayjs";
import MyCheckBox from "@/components/MyCheckBox/MyCheckBox.vue";
import MyInputNumber from "@/components/MyInputNumber/MyInputNumber.vue";
import YjCloseableTag from "@/components/YjCloseableTag.vue";
import {uniChooseMedia} from "@/utils/uni";
import MyButton from "@/components/MyButton/MyButton.vue";
import {useGlobalStore} from "@/store/global";
import {onLoad, onShow} from "@dcloudio/uni-app";
import {qiniuUpload} from "@/utils/upload";
import {isMobileFunc} from "@/utils/validators";
import {wishAiSuggest, wishSubmit} from "@/api/wish";

const globalStore = useGlobalStore()

const contentStyle = ref({})
const budgetOptions = ['1000元以下', '1000-3000元', '3000-5000元', '5000-8000元', '8000元以上']
const formData = ref({
  from: '',
  to: '',
  budget: budgetOptions[0],
  budget_type: BudgetType.Single,
  customBudget: '',
  date: '',
  deadline: '',
  month: '',
  todos: [],
  total_people: 2,
  member_desc: '',
  title: '',
  wish_desc: '',
  tags: [],
  medias: [],
  phone: '',
  open_scope: WishOpenScope.Public,
});
const toPoi = ref({})
const budgetCustom = ref(false)
const DateType = {
  Date: 1,
  Month: 2,
  Unknown: 3
}
const MediaTypeImage = WishMediaType
const newTodo = ref({
  todo: '',
  is_must: Disable,
})
const formDateDateType = ref(DateType.Unknown)
const showCalendar = ref(false);
const calendarMode = ref('single')
const showAddTodo = ref(false)
const dateText = computed(() => {
  if (!formData.value.date) return '';

  if (Array.isArray(formData.value.date)) {
    if (formData.value.date.length < 2) return '';

    const [start, end] = formData.value.date;
    const daysDiff = dayjs(end).diff(dayjs(start), 'day') + 1;
    return `${dayjs(start).format('YYYY年M月D日')}至${dayjs(end).format('YYYY年M月D日')}（共${daysDiff}天）`;
  } else if (formData.value.date) {
    return dayjs(formData.value.date).format('YYYY年M月');
  }
  return '';
})

watch(() => globalStore.data, (d) => {
  if (d && d.type === 'poi') {
    toPoi.value = d.data
    formData.value.to = toPoi.value.name

    globalStore.clearData()
  }
})

function onAi(type) {
  const to = formData.value.to?.trim() || '';
  if (to.length === 0) {
    showToast('请先选择目的地，我在帮你想一下');
    return
  }

  let prompt = `出发地：${formData.value.from}，目的地：${formData.value.to}；总人数：${formData.value.total_people}人`
  if (formData.value.budget) {
    if (formData.value.budget_type === BudgetType.Single) {
      prompt += `；预算：${formData.value.budget}/每人`
    } else {
      prompt += `；总体预算：${formData.value.budget}`
    }
  }

  const data = {
    prompt,
    type,
  }

  wishAiSuggest(data).then(({data}) => {
    if (!data.suggests || data.suggests.length === 0) {
      showToast('没有结果');
      return
    }
    switch (type) {
      case 'todos':
        formData.value.todos = data.suggests.map(todo => {
          return {
            todo,
            is_must: Disable,
          }
        })
        break;
      case 'subject':
        formData.value.title = data.suggests[0]
        break;
      case 'desc':
        formData.value.wish_desc = data.suggests[0]
    }

  })
}

const openCalendar = (mode) => {
  formDateDateType.value = mode
  if (mode === DateType.Unknown) {
    formData.value.date = ''
    return
  }

  calendarMode.value = mode === DateType.Date ? 'range' : 'month'
  showCalendar.value = true
};

const handleCalendarConfirm = (value) => {
  formData.value.date = value
};

const handleBudgetChange = (value) => {
  budgetCustom.value = false
  formData.value.budget = value
};

const addTodo = () => {
  newTodo.value.todo = ''
  showAddTodo.value = true
};

function onConfirmAddTodo(e) {
  newTodo.value.todo = e

  formData.value.todos.push(deepToRaw(newTodo))

  showAddTodo.value = false
}

const removeTodo = (index) => {
  formData.value.todos.splice(index, 1);
};

const addTag = () => {
  showPrompt('请输入标签名称', '').then(v => {
    v = v.trim()
    if (v === '' || formData.value.tags.includes(v)) {
      return
    }

    formData.value.tags.push(v)
  })
};

const removeTag = (index) => {
  formData.value.tags.splice(index, 1);
};

const uploadMedia = () => {
  if (formData.value.medias.length >= 9) {
    showToast('最多上传9个视频或图片');
    return;
  }

  uniChooseMedia({size: parseFileSize('20m')}).then(async (res) => {
    const path = await qiniuUpload(res, 3)
    const type = path.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i) ? MediaTypeImage.Image : MediaTypeImage.Video;
    formData.value.medias.push({type, url: path});
  }).catch(e => showToast(e.toString()))
};

const removemedias = (index) => {
  formData.value.medias.splice(index, 1);
};

const handleOpenScopeChange = (e) => {
  formData.value.open_scope = e.detail.value;
};

const onSubmit = () => {
  if (!formData.value.to) {
    showToast('请选择目的地')
    return
  } else if (!isMobileFunc(formData.value.phone)) {
    showToast('请输入正确的手机号码')
    return
  } else if (!formData.value.title || !formData.value.wish_desc) {
    showToast('请输入心愿主题和描述')
    return
  } else if (formData.value.medias.length === 0) {
    showToast('请上传图片或视频')
    return
  }

  if (Array.isArray(formData.value.date) && formData.value.date.length === 2) {
    formData.value.depart_date = formData.value.date[0]
    formData.value.return_date = formData.value.date[1]
  } else {
    formData.value.depart_date = formData.value.date
  }
  formData.value.todos.map(item => {
    item.is_must = item.is_must === true ? Enable : Disable
  })

  // 设置 deadline 逻辑
  if (formData.value.depart_date) {
    formData.value.deadline = formData.value.depart_date;
  } else {
    // 如果没有 depart_date，则往后推 1 个月
    formData.value.deadline = dayjs().add(1, 'month').toDate();
  }

  // 转换为时间戳
  formData.value.deadline = dayjs(formData.value.deadline).unix()

  const data = deepToRaw(formData.value)
  data.to_poi = toPoi.value.location
  data.to_zone_id = toPoi.value.zone_id ?? toPoi.value.id
  data.todos = JSON.stringify(data.todos)
  data.medias = JSON.stringify(data.medias)
  data.tags = data.tags.join(',')

  wishSubmit(data).then(() => {
    showToast('心愿发布成功了').then(() => {
      navTo('pages/wish-square/wish-square', {sort: 'ctime'})
    })
  })
};

onLoad(() => {
  globalStore.getLocation().then((res) => {
    formData.value.from = res.city
  })
})

onShow(() => {

})

</script>

<style lang="scss">
@use "publish-wish";
</style>
