import dayjs from 'dayjs'
import {isProxy, isReactive, isRef, nextTick, toRaw, unref} from 'vue';
import {holiday} from "@/api";

const w = {
  0: '周日',
  1: '周一',
  2: '周二',
  3: '周三',
  4: '周四',
  5: '周五',
  6: '周六',
}

export function copyText(str, showToast = true) {
  return new Promise((resolve, reject) => {
    uni.setClipboardData({
      data: str,
      showToast,
      success: resolve,
      fail: reject,
    })
  })
}


export function scrollToBottom(id = 'container', instance = null) {
  nextTick(() => {
    // 获取页面高度
    let query = uni.createSelectorQuery()
    if (instance) {
      query = query.in(instance.proxy)
    }
    query.select(`#${id}`).boundingClientRect(res => {
      if (res) {
        uni.pageScrollTo({
          scrollTop: res.height,
          duration: 300 // 滚动动画时长，单位ms
        });
      }
    }).exec();
  })

}

export function throttle(func, wait) {
  let timeout = null;
  let lastArgs = null;
  let lastThis = null;
  let lastCallTime = 0;
  let leading = true;
  let trailing = true;

  function throttled(...args) {
    const now = Date.now();
    lastThis = this;
    lastArgs = args;

    if (lastCallTime === 0 && leading === false) {
      lastCallTime = now;
    }

    const remaining = wait - (now - lastCallTime);

    function callIt() {
      lastCallTime = leading ? now : Date.now();
      func.apply(lastThis, lastArgs);
      lastArgs = lastThis = null;
    }

    if (remaining <= 0) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      callIt();
    } else if (!timeout && trailing !== false) {
      timeout = setTimeout(() => {
        if (trailing === false) return;
        if (lastArgs) {
          callIt();
        }
      }, remaining);
    }
  }

  throttled.cancel = function () {
    if (timeout) {
      clearTimeout(timeout);
      lastCallTime = 0;
      lastArgs = lastThis = timeout = null;
    }
  };

  return throttled;
}

export function moveElement(arr, index, direction) {
  // 检查是否提供了有效的数组、索引和方向
  if (!Array.isArray(arr) || index < 0 || index >= arr.length || (direction !== 'forward' && direction !== 'backward')) {
    return false
  }

  // 如果是向前移动，并且不是第一个元素
  if (direction === 'forward' && index > 0) {
    [arr[index - 1], arr[index]] = [arr[index], arr[index - 1]];
    return true
  }
  // 如果是向后移动，并且不是最后一个元素
  else if (direction === 'backward' && index < arr.length - 1) {
    [arr[index], arr[index + 1]] = [arr[index + 1], arr[index]];
    return true
  }

  return false
}

export function richtext(content, imgBlock = true) {
  let sty = 'max-width:100%;height:auto;'
  if (imgBlock) {
    sty += 'display:block;'
  }
  let styReg = /style=['"](.*?)['"]/
  content = content.replaceAll(/<img(.*?)>/g, (_, s) => {
    let mat = s.match(styReg)
    if (mat?.length == 2) {
      s = s.replace(styReg, (_, stys) => {
        return `style="${sty}${stys}"`
      })
      return `<img${s}>`
    } else {
      return `<img style="${sty}" ${s}>`
    }
  })

  // 使用正则表达式为 ul 添加样式
  content = content.replace(/<ul>/g, '<ul style="margin:0;padding:0 5px;list-style:disc;">');

  // 使用正则表达式为 ul 添加样式
  content = content.replace(/<ol>/g, '<ol style="margin:0;padding:0 5px;list-style:disc;">');

  return content
}

export function weekday(d) {
  const time = dayjs(d)

  return w[time.day()]
}

export function encodeObjToUrlParams(obj) {
  return Object.keys(obj).map(key => {
    if (Array.isArray(obj[key])) {
      return obj[key].map(value => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`).join('&');
    } else {
      return `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`;
    }
  }).join('&');
}

export function navBack(delta = 1) {
  if (getCurrentPages().length > 1) {
    uni.navigateBack({
      delta
    });
  } else {
    uni.reLaunch({
      url: '/pages/index/index'
    });
  }
}

export function decodeQuery(obj) {
  for (let i in obj) {
    obj[i] = decodeURIComponent(obj[i]);
  }

  return obj
}

/**
 * 跳转到指定页面url
 * 支持tabBar页面
 * @param {string}  url   页面路径
 * @param {object}  query 页面参数
 * @param {boolean}  redirect  跳转类型(默认navigateTo)
 */
export function navTo(url, query = {}, redirect = false) {
  return new Promise((resolve, reject) => {
    if (!url || url.length === 0) {
      return reject('url不能为空')
    }

    // 检查是否是http/https链接
    if (url.startsWith('http://') || url.startsWith('https://')) {
      // 生成query参数并拼接到url
      const queryStr = encodeObjToUrlParams(query)
      const fullUrl = queryStr ? `${url}?${queryStr}` : url
      return uni.navigateTo({
        url: `/pages/h5/h5?url=${encodeURIComponent(fullUrl)}`
      })
    }

    url = url.replace(/^\//, '')
    // 生成query参数
    let queryStr = encodeObjToUrlParams(query)
    if (url.indexOf('?') === -1) {
      url += '?'
    }

    // 普通页面, 使用navigateTo
    !redirect && uni.navigateTo({
      url: `/${url}${queryStr}`,
      success: resolve,
      fail: reject
    })
    // 特殊指定, 使用redirectTo
    redirect && uni.redirectTo({
      url: `/${url}${queryStr}`,
      success: resolve,
      fail: reject
    })

    return true
  })
}

export function getParamsFromUrl(url) {
  if (url.indexOf('?') != -1) {
    const queryArr = url.substring(url.indexOf('?') + 1).replace(/[#/|/#/]/g, "").split('&')
    const params = queryArr.length > 0 ? {} : null
    queryArr.map(item => {
      let p = item.split('=')
      params[p[0]] = decodeURIComponent(p[1])
    })
    return params
  } else {
    return ''
  }
}

/**
 * 将米转换为公里
 * @param {number} meters - 米数
 * @param {number} [decimalPlaces=2] - 保留的小数位数
 * @returns {number} 公里数
 */
export function formatMetersToKilometers(meters, decimalPlaces = 2) {
  if (typeof meters !== 'number' || isNaN(meters)) {
    return 0
  }
  const kilometers = meters / 1000;
  return parseFloat(kilometers.toFixed(decimalPlaces));
}


export function formatDiscount(v) {
  return (v * 10).toString() + '折'
}

export function formatTime(t, format = 'YYYY.M.D') {
  const d = (t instanceof Date) ? dayjs(t) : dayjs.unix(t)

  return d.format(format)
}

export function showLoading(title = '加载中...') {
  uni.showLoading({
    title,
    mask: true
  })
}

export function hideLoading() {
  uni.hideLoading()
}

export const showModal = (title = '', content = '') => {
  return new Promise((resolve, reject) => {
    uni.showModal({
      title,
      content,
      success: resolve,
      fail: reject,
    })
  })
}

/**
 * 显示prompt弹窗
 */
export const showPrompt = (title, placeholderText = '') => {
  return new Promise((resolve, reject) => {
    uni.showModal({
      title,
      editable: true,
      placeholderText,
      content: placeholderText,
      success({
                confirm,
                cancel,
                content
              }) {
        if (confirm) {
          resolve(content)
        } else {
          reject(cancel)
        }
      },
      fail: reject,
    })
  })
}

/**
 * 显示确认弹窗
 */
export const showConfirm = (title, content) => {
  return new Promise((resolve, reject) => {
    uni.showModal({
      title,
      content,
      showCancel: true,
      success({
                confirm
              }) {
        confirm ? resolve() : reject()
      },
      fail: reject,
    })
  })
}

/**
 * 显示失败提示框
 */
export const showError = (msg, showCancel = false, title = '友情提示') => {
  return new Promise((resolve, reject) => {
    uni.showModal({
      title,
      content: msg,
      showCancel,
      success({
                confirm
              }) {
        confirm ? resolve() : reject()
      },
      fail: reject,
    })
  })
}

export function showToast(title, icon = 'none', duration = 1500) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      uni.showToast({
        title,
        icon,
        duration,
        mask: true,
      })

      setTimeout(() => {
        resolve()
      }, duration + 100)
    }, 300)
  })
}

export function formatMoney(number, fixed = 2, symbol = '¥') {
  // 先转换为字符串并固定两位小数（可根据需求调整位数）
  let formatted = (number / 100).toFixed(fixed);

  while (true) {
    const p = formatted.lastIndexOf('.')
    if (p > 0 && formatted.endsWith("0")) {
      formatted = formatted.replace(/0+$/, '')
      continue
    }
    break
  }
  if (formatted.endsWith(".")) {
    formatted = formatted.replace('.', '')
  }

  // 如果需要添加货币符号，例如美元

  return symbol + formatted;
}

/**
 * 递归地将响应式数据（包括 reactive 和 ref）转换为原始数据
 * @param {Object|Array|Ref} obj - 要转换的对象或 ref
 * @param {WeakMap} [seen] - 用于处理循环引用的WeakMap
 * @returns {Object|Array|any} 原始数据
 */
/**
 * 将人可读的文件大小（如 "5M"）转换为机器可读的字节数
 * @param {string} sizeStr - 人可读的文件大小（如 "5M"、"1G"）
 * @returns {number} 字节数
 */
export function parseFileSize(sizeStr) {
  const units = {
    B: 1,
    K: 1024,
    M: 1024 * 1024,
    G: 1024 * 1024 * 1024,
    T: 1024 * 1024 * 1024 * 1024,
  };

  const match = sizeStr.match(/^(\d+)([BKMGTbkmgt])?$/i);
  if (!match) {
    throw new Error('Invalid size format');
  }

  const value = parseFloat(match[1]);
  const unit = match[2] ? match[2].toUpperCase() : 'B';

  if (!units[unit]) {
    throw new Error('Invalid size unit');
  }

  return value * units[unit];
}

/**
 * 递归地将响应式数据（包括 reactive 和 ref）转换为原始数据
 * @param {Object|Array|Ref} obj - 要转换的对象或 ref
 * @param {WeakMap} [seen] - 用于处理循环引用的WeakMap
 * @returns {Object|Array|any} 原始数据
 */
export function deepToRaw(obj, seen = new WeakMap()) {
  // 基本类型直接返回
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  // 检查循环引用
  if (seen.has(obj)) {
    return seen.get(obj);
  }

  // 处理Ref
  if (isRef(obj)) {
    obj = unref(obj);
  }

  // 处理Reactive
  if (isProxy(obj) && isReactive(obj)) {
    obj = toRaw(obj);
  }

  // 处理Date
  if (obj instanceof Date) {
    const copy = new Date(obj.getTime());
    seen.set(obj, copy);
    return copy;
  }

  // 处理Map
  if (obj instanceof Map) {
    const copy = new Map();
    seen.set(obj, copy);
    for (const [key, val] of obj.entries()) {
      copy.set(key, deepToRaw(val, seen));
    }
    return copy;
  }

  // 处理Set
  if (obj instanceof Set) {
    const copy = new Set();
    seen.set(obj, copy);
    for (const item of obj) {
      copy.add(deepToRaw(item, seen));
    }
    return copy;
  }

  // 处理数组
  if (Array.isArray(obj)) {
    const copy = [];
    seen.set(obj, copy);
    for (let i = 0; i < obj.length; i++) {
      copy[i] = deepToRaw(obj[i], seen);
    }
    return copy;
  }

  // 处理普通对象
  const copy = {};
  seen.set(obj, copy);
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      copy[key] = deepToRaw(obj[key], seen);
    }
  }

  // 处理Symbol属性
  const symbolKeys = Object.getOwnPropertySymbols(obj);
  for (const symKey of symbolKeys) {
    copy[symKey] = deepToRaw(obj[symKey], seen);
  }

  return copy;
}

export function deepClone(source) {
  const data = JSON.stringify(source)
  return JSON.parse(data)
}

/**
 * 判断是否为数组
 * @param {*} array
 */
export const isArray = (array) => {
  return Object.prototype.toString.call(array) === '[object Array]'
}

/**
 * 获取CDN完整URL
 * @param {string} staticPath - 静态资源路径，如 '/static/icons/calendar.png'
 * @returns {string} 完整的CDN URL
 */
export function getCdnUrl(staticPath) {
  const cdnBaseUrl = import.meta.env.VITE_CDN_BASE_URL || 'https://rp.yjsoft.com.cn/yiban'
  // 移除路径开头的斜杠，确保路径格式正确
  const cleanPath = staticPath.replace(/^\//, '')
  return `${cdnBaseUrl}/${cleanPath}`
}

/**
 * 获取H5页面完整URL
 * @param {string} path - H5页面路径，如 '/index.html'
 * @returns {string} 完整的H5 URL
 */
export function getH5Url(path) {
  const h5BaseUrl = import.meta.env.VITE_H5_BASE_URL || 'https://h5.funfuntrip.cn'
  // 移除路径开头的斜杠，确保路径格式正确
  const cleanPath = path.replace(/^\//, '')
  return `${h5BaseUrl}/${cleanPath}`
}

export async function holiday4Calendar(month = '') {
  const selectMonth = month ? dayjs(month, 'YYYY-MM') : dayjs();
  
  const params = {
    start: selectMonth.startOf('month').unix(),
    end: selectMonth.endOf('month').unix()
  }
  
  const {data} = await holiday(params, false)
  
  return data.holidays.filter(item => item.festival)
}
