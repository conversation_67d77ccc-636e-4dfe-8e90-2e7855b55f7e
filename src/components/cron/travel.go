package cron

import (
	"context"
	"github.com/duke-git/lancet/v2/slice"
	"go.uber.org/zap"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_dify"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
	"time"
)

// 每天定时清理一个月前的行程历史
func clearPlanHistory() {
	db := models.New()
	tm := time.Now().AddDate(0, -1, 0)
	for {
		var list []models.PlanHistory
		if ret := db.Unscoped().Where("created_at<?", tm).Limit(10).Delete(&list); ret.Error != nil {
			my_logger.Errorf("clear plan history error", zap.Error(ret.Error))
			return
		} else if ret.RowsAffected == 0 {
			break
		}
	}
}

// 每月热门城市
func hotZones() {
	db := models.New()
	if hots, err := my_dify.HotZones(context.Background(), db); err != nil || len(hots.Cities) == 0 {
		my_logger.Errorf("[hotZones]dify.HotZones", zap.Error(err))
	} else {
		var zs = make([]models.Zone, 0)
		db.Where(models.Zone{Level: constmap.ZoneLevelCity, State: constmap.Enable}).Find(&zs)
		zs = slice.Filter(zs, func(index int, v models.Zone) bool {
			return slice.ContainBy(hots.Cities, func(item string) bool {
				return strings.Contains(v.Name, item)
			})
		})

		zs = zone_biz.NewZoneBiz().NormalizeZoneNames(zs)

		cacheData := slice.Map(zs, func(index int, item models.Zone) beans.HotZone {
			return beans.HotZone{
				Id:   item.ID,
				Name: item.Name,
				Lng:  item.Lng,
				Lat:  item.Lat,
			}
		})
		if err := my_cache.Set(constmap.RKHotZones, utils.NewGenericList(cacheData), constmap.TimeDur40d); err != nil {
			my_logger.Errorf("[hotZones]cache.Set", zap.Error(err))
		} else {
			my_logger.Infof("[hotZones]cache.Set", zap.Any("data", hots))
		}
	}
}
